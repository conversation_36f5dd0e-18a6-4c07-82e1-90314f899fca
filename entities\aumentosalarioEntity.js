import toJSON from './toJSON.js';

export default class AumentoSalarioEntity extends toJSON {
    #aus_id;
    #aus_data;
    #aus_percentual;
    #usu_id;

    get aus_id() { return this.#aus_id;} set aus_id(aus_id) { this.#aus_id = aus_id; }
    get aus_data() { return this.#aus_data;} set aus_data(aus_data) { this.#aus_data = aus_data; }
    get aus_percentual() { return this.#aus_percentual;} set aus_percentual(aus_percentual) { this.#aus_percentual = aus_percentual; }
    get usu_id() { return this.#usu_id;} set usu_id(usu_id) { this.#usu_id = usu_id; }

    constructor(data, percentual, usuario) {
        super();
        this.aus_data = data;
        this.aus_percentual = percentual;
        this.usu_id = usuario;
    }
}
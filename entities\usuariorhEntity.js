import toJSON from './toJSON.js';

export default class UsuarioRHEntity extends toJSON {
    #usu_id;
    #usu_nome;
    #usu_email;
    #usu_ativo;
    #usu_senha;

    get usu_id() { return this.#usu_id;} set usu_id(usu_id) { this.#usu_id = usu_id; }
    get usu_nome() { return this.#usu_nome;} set usu_nome(usu_nome) { this.#usu_nome = usu_nome; }
    get usu_email() { return this.#usu_email;} set usu_email(usu_email) { this.#usu_email = usu_email; }
    get usu_ativo() { return this.#usu_ativo;} set usu_ativo(usu_ativo) { this.#usu_ativo = usu_ativo; }
    get usu_senha() { return this.#usu_senha;} set usu_senha(usu_senha) { this.#usu_senha = usu_senha; }

    constructor(nome, email, senha) {
        super();
        this.usu_nome = nome;
        this.usu_email = email;
        this.usu_senha = senha;
        this.usu_ativo = true;
    }
}
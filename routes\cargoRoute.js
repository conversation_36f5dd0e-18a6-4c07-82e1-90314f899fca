import express from 'express';
import CargoController from '../controllers/cargoController.js';
import AuthMiddleware from '../middlewares/authMiddleware.js';

const router = express.Router();
const cargoController = new CargoController();
const authMiddleware = new AuthMiddleware();

// Middleware de autenticação para todas as rotas
router.use((req, res, next) => authMiddleware.validar(req, res, next));

// Rotas de cargos
router.get('/', (req, res) => cargoController.listar(req, res)
);
export default router;

import toJSON from './toJSON.js';

export default class ItensFolhaPagamentoEntity extends toJSON {
    #ifp_id;
    #ifp_salario;
    #fun_id;
    #fol_id;

    get ifp_id() { return this.#ifp_id;} set ifp_id(ifp_id) { this.#ifp_id = ifp_id; }
    get ifp_salario() { return this.#ifp_salario;} set ifp_salario(ifp_salario) { this.#ifp_salario = ifp_salario; }
    get fun_id() { return this.#fun_id;} set fun_id(fun_id) { this.#fun_id = fun_id; }
    get fol_id() { return this.#fol_id;} set fol_id(fol_id) { this.#fol_id = fol_id; }

    constructor(salario, funcionario, folha) {
        super();
        this.ifp_salario = salario;
        this.fun_id = funcionario;
        this.fol_id = folha;
    }
}
import Database from '../db/database.js';
import ItensFolhaPagamentoEntity from '../entities/itensfolhapagamentoEntity.js';

export default class ItensFolhaPagamentoRepo {

    #db;

    set db(db) { this.#db = db; }

    constructor() {
        this.#db = new Database();
    }

    async Inserir(item) {
        let sql = "INSERT INTO tb_itensfolhapagamento (ifp_salario, fun_id, fol_id) VALUES (?, ?, ?)";
        let valores = [item.ifp_salario, item.fun_id, item.fol_id];
        let results = await this.#db.ExecutaComando(sql, valores);
        return results;
    }

    async Listar() {
        let sql = "SELECT * FROM tb_itensfolhapagamento";
        let results = await this.#db.ExecutaComando(sql, []);
        return results;
    }

    async ListarPorFolha(id) {
        let sql = "SELECT * FROM tb_itensfolhapagamento WHERE fol_id = ?";
        let valores = [id];
        let results = await this.#db.ExecutaComando(sql, valores);
        return results;
    }

    async ListarPorFuncionario(id) {
        let sql = "SELECT * FROM tb_itensfolhapagamento WHERE fun_id = ?";
        let valores = [id];
        let results = await this.#db.ExecutaComando(sql, valores);
        return results;
    }

}
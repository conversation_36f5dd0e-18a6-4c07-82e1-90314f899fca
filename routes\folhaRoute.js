import express from 'express';
import FolhaController from '../controllers/folhaController.js';
import AuthMiddleware from '../middlewares/authMiddleware.js';

const router = express.Router();
const folhaController = new FolhaController();
const authMiddleware = new AuthMiddleware();

// Middleware de autenticação para todas as rotas
router.use((req, res, next) => authMiddleware.validar(req, res, next));

// Rotas de folha de pagamento
router.post('/gerar', (req, res) => folhaController.gerarFolha(req, res)
);
router.get('/', (req, res) => folhaController.listarFolhas(req, res)
);

export default router;
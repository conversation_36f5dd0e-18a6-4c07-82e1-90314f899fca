import swaggerAutogen from "swagger-autogen";

const doc = {
  host: "localhost:5000",
  basePath: "/",
  schemes: ["http"],
  consumes: ["application/json"],
  produces: ["application/json"],
  info: {
    title: "Sistema de Gerenciamento de RH - API",
    description: "API completa para gerenciamento do departamento de Recursos Humanos, incluindo cadastro de funcionários, aplicação de aumentos salariais e geração de folhas de pagamento.",
    version: "1.0.0",
    contact: {
          name: "DevMinds Team",
            email: "<EMAIL>"
    }
  },
  tags: [
    {
      name: "Autenticação",
      description: "Endpoints para autenticação de usuários",
    },
    {
      name: "Funcionários",
      description: "Gerenciamento de funcionários",
    },
    {
      name: "Cargos",
      description: "Gerenciamento de cargos",
    },
    {
      name: "Aumento Salarial",
      description: "Aplicação e histórico de aumentos salariais",
    },
    {
      name: "<PERSON><PERSON><PERSON>nto",
      description: "Geração e consulta de folhas de pagamento",
    },
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: "http",
        scheme: "bearer",
        bearerFormat: "JWT",
        description: "Token JWT obtido através do endpoint de login",
      },
    },
    responses: {
      UnauthorizedError: {
        description: "Token de acesso inválido ou ausente",
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                message: { type: "string", example: "Token inválido!" },
              },
            },
          },
        },
      },
      InternalServerError: {
        description: "Erro interno do servidor",
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                message: {
                  type: "string",
                  example: "Erro interno do servidor",
                },
              },
            },
          },
        },
      },
    },
  },
};

const outputJson = "./swagger-output.json";
const routes = [
  "./server.js",
  "./routes/authRoute.js",
  "./routes/funcionarioRoute.js",
  "./routes/cargoRoute.js",
  "./routes/aumentoSalarioRoute.js",
  "./routes/folhaRoute.js",
];

swaggerAutogen({ openapi: "3.0.0" })(outputJson, routes, doc).then(async () => {
  console.log(" Documentação Swagger gerada com sucesso!");
  await import("./server.js");
});

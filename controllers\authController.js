import UsuarioRHRepo from '../repositories/usuariorhRepo.js';
import AuthMiddleware from '../middlewares/authMiddleware.js';

export default class AuthController {

    #repo;
    #middleware;

    constructor() {
        this.#repo = new UsuarioRHRepo();
        this.#middleware = new AuthMiddleware();
    }

    async autenticar(req, res) {
        try {
            let {email, senha} = req.body;
            let results = await this.#repo.Autenticar(email, senha);
            if(results.length == 0) {
                res.status(401).json({msg: "Usuário ou senha inválidos!"});
                return;
            }
            let usuario = results[0];
            let token = this.#middleware.gerarToken(usuario.usu_id, usuario.usu_nome, usuario.usu_email);
            res.status(200).json({msg: "Autenticado com sucesso!", token: token});
        }
        catch(ex) {
            res.status(500).json({msg: "Erro ao autenticar usuário!"});
        }
    }

}
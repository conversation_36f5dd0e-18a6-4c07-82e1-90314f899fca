{"name": "", "version": "1.0.0", "description": "", "main": "server.js", "type": "module", "license": "AGPL-version-3.0", "private": false, "engines": {"node": ">= 14.0.0", "npm": ">= 6.0.0"}, "homepage": "", "repository": {"type": "git", "url": ""}, "bugs": "", "keywords": [], "author": {"name": "", "email": "", "url": ""}, "contributors": [], "scripts": {"dev": "node server.js", "test": "echo \"No tests specified\"", "start": "node swagger.js", "server": "node server.js", "swagger": "node swagger.js"}, "dependencies": {"cookie-parser": "^1.4.6", "express": "^4.19.2", "jsonwebtoken": "^9.0.2", "mysql2": "^3.11.0", "swagger-autogen": "^2.23.7", "swagger-ui-express": "^5.0.1"}}
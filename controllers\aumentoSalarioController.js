import AumentoSalarioRepo from '../repositories/aumentosalarioRepo.js';
import FuncionarioRepo from '../repositories/funcionarioRepo.js';
import AumentoSalarioEntity from '../entities/aumentosalarioEntity.js';
import Database from '../db/database.js';

export default class AumentoSalarioController {

    #aumentoRepo;
    #funcionarioRepo;

    constructor() {
        this.#aumentoRepo = new AumentoSalarioRepo();
        this.#funcionarioRepo = new FuncionarioRepo();
    }

    async aplicarAumento(req, res) {
        let db = new Database();
        this.#aumentoRepo.db = db;
        this.#funcionarioRepo.db = db;
        
        await db.AbreTransacao();
        try {
            let { percentual } = req.body;
            
            if (!percentual || percentual <= 0) {
                res.status(400).json({ msg: "Percentual é obrigatório e deve ser maior que 0!" });
                return;
            }

            // Aplicar aumento a todos os funcionários ativos
            await this.#funcionarioRepo.AplicarAumentoSalarial(percentual);
            
            // Registrar o aumento na tabela de histórico
            let dataAtual = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
            let aumento = new AumentoSalarioEntity(dataAtual, percentual, req.usuario.usu_id);
            await this.#aumentoRepo.Inserir(aumento);
            
            await db.Commit();
            
            res.status(200).json({ 
                msg: "Aumento salarial aplicado com sucesso!",
                percentual: percentual,
                data: dataAtual,
                usuario: req.usuario.usu_nome
            });
        } catch (ex) {
            await db.Rollback();
            res.status(500).json({ msg: "Erro ao aplicar aumento salarial!", error: ex.message });
        }
    }

    async listarHistorico(_, res) {
        try {
            let historico = await this.#aumentoRepo.Listar();
            res.status(200).json(historico);
        } catch (ex) {
            res.status(500).json({ msg: "Erro ao listar histórico de aumentos!", error: ex.message });
        }
    }
}

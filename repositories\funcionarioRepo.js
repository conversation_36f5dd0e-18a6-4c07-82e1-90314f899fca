import Database from '../db/database.js';
import FuncionarioEntity from '../entities/funcionarioEntity.js';

export default class FuncionarioRepo {

    #db;
    
    set db(db) { this.#db = db; }

    constructor() {
        this.#db = new Database();
    }

    async Inserir(funcionario) {
        let funcionarioExistente = await this.BuscarPorCPF(funcionario.fun_cpf);
        if (funcionarioExistente.length > 0) {
            throw new Error("Funcionário já cadastrado!");
        }

        let sql = "INSERT INTO tb_funcionario (fun_cpf, fun_nome, fun_salario, fun_datadmissao, car_id) VALUES (?, ?, ?, ?, ?)";
        let valores = [funcionario.fun_cpf, funcionario.fun_nome, funcionario.fun_salario, funcionario.fun_datadmissao, funcionario.car_id];
        let results = await this.#db.ExecutaComando(sql, valores);
        return results;
    }

    async Listar() {
        let sql = "SELECT * FROM tb_funcionario WHERE fun_datademissao IS NULL";
        let results = await this.#db.ExecutaComando(sql, []);
        return results;
    }

    async Demitir(id) {
        let funcionarioJaDemitido = await this.BuscarPorId(id);
        if (funcionarioJaDemitido.fun_datademissao) {
            throw new Error("Funcionário já demitido!");
        }
        let sql = "UPDATE tb_funcionario SET fun_datademissao = CURDATE() WHERE fun_id = ?";
        let valores = [id];
        let results = await this.#db.ExecutaComandoNonQuery(sql, valores);
        return results;
    }

    async ListarPorCargo(id) {
        let sql = "SELECT * FROM tb_funcionario WHERE car_id = ? AND fun_datademissao IS NULL";
        let valores = [id];
        let results = await this.#db.ExecutaComando(sql, valores);
        return results;
    }

    async AplicarAumentoSalarial(percentual) {
        let sql = "UPDATE tb_funcionario SET fun_salario = fun_salario * (1 + ? / 100) WHERE fun_datademissao IS NULL";
        let valores = [percentual];
        let results = await this.#db.ExecutaComandoNonQuery(sql, valores);
        return results;
    }

    async BuscarPorId(id) {
        let sql = "SELECT * FROM tb_funcionario WHERE fun_id = ?";
        let valores = [id];
        let results = await this.#db.ExecutaComando(sql, valores);
        return results[0];
    }

    async BuscarPorCPF(cpf) {
        let sql = "SELECT * FROM tb_funcionario WHERE fun_cpf = ?";
        let valores = [cpf];
        let results = await this.#db.ExecutaComando(sql, valores);
        return results;
    }

}
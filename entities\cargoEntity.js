import toJSON from './toJSON.js';

export default class CargoEntity extends toJSON {
    #car_id;
    #car_descricao;

    get car_id() { return this.#car_id;} set car_id(car_id) { this.#car_id = car_id; }
    get car_descricao() { return this.#car_descricao;} set car_descricao(car_descricao) { this.#car_descricao = car_descricao; }

    constructor(descricao) {
        super();
        this.car_descricao = descricao;
    }
}
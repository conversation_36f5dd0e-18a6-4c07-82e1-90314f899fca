import Database from '../db/database.js';
import UsuarioRHEntity from '../entities/usuariorhEntity.js';

export default class UsuarioRHRepo {

    #db;

    constructor() {
        this.#db = new Database();
    }

    async Autenticar(email, senha) {
        let sql = "SELECT * FROM tb_usuariorh WHERE usu_email = ? AND usu_senha = ? AND usu_ativo = 1";
        let valores = [email, senha];
        let results = await this.#db.ExecutaComando(sql, valores);
        return results;
    }

    async Listar() {
        let sql = "SELECT * FROM tb_usuariorh";
        let results = await this.#db.ExecutaComando(sql, []);
        return results;
    }

    async Inserir(usuario) {
        let sql = "INSERT INTO tb_usuariorh (usu_nome, usu_email, usu_ativo, usu_senha) VALUES (?, ?, ?, ?)";
        let valores = [usuario.usu_nome, usuario.usu_email, usuario.usu_ativo, usuario.usu_senha];
        let results = await this.#db.ExecutaComando(sql, valores);
        return results;
    }

    async Atualizar(usuario) {
        let sql = "UPDATE tb_usuariorh SET usu_nome = ?, usu_email = ?, usu_ativo = ?, usu_senha = ? WHERE usu_id = ?";
        let valores = [usuario.usu_nome, usuario.usu_email, usuario.usu_ativo, usuario.usu_senha, usuario.usu_id];
        let results = await this.#db.ExecutaComandoNonQuery(sql, valores);
        return results;
    }

    async Excluir(id) {
        //exclusão lógica
        let sql = "UPDATE tb_usuariorh SET usu_ativo = 0 WHERE usu_id = ?";
        let valores = [id];
        let results = await this.#db.ExecutaComandoNonQuery(sql, valores);
        return results;
    }

    async BuscarPorId(id) {
        let sql = "SELECT * FROM tb_usuariorh WHERE usu_id = ?";
        let valores = [id];
        let results = await this.#db.ExecutaComando(sql, valores);
        return results;
    }

}
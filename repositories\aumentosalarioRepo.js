import Database from '../db/database.js';
import AumentoSalarioEntity from '../entities/aumentosalarioEntity.js';

export default class AumentoSalarioRepo {

    #db;

    set db(db) { this.#db = db; }

    constructor() {
        this.#db = new Database();
    }

    async Inserir(aumento) {
        try {
            let sql = "INSERT INTO tb_aumentosalario (aus_data, aus_percentual, usu_id) VALUES (?, ?, ?)";
            let valores = [aumento.aus_data, aumento.aus_percentual, aumento.usu_id];
            let id = await this.#db.ExecutaComandoLastInserted(sql, valores);
            return id;
        } catch (err) {
            throw err;
        }
    }

    async Listar() {
        let sql = "SELECT * FROM tb_aumentosalario ORDER BY aus_data DESC";
        let results = await this.#db.ExecutaComando(sql, []);
        return results;
    }

}
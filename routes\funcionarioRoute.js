import express from 'express';
import FuncionarioController from '../controllers/funcionarioController.js';
import AuthMiddleware from '../middlewares/authMiddleware.js';

const router = express.Router();
const funcionarioController = new FuncionarioController();
const authMiddleware = new AuthMiddleware();

// Middleware de autenticação para todas as rotas
router.use((req, res, next) => authMiddleware.validar(req, res, next));

// Rotas de funcionários
router.post('/', (req, res) => funcionarioController.cadastrar(req, res)
);

router.get('/', (req, res) => funcionarioController.listar(req, res)
);

router.get('/:id', (req, res) => funcionarioController.buscarPorId(req, res)
);

router.delete('/:id', (req, res) => funcionarioController.demitir(req, res)
);


export default router;

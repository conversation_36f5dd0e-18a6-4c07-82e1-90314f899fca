import express from 'express';
import AumentoSalarioController from '../controllers/aumentoSalarioController.js';
import AuthMiddleware from '../middlewares/authMiddleware.js';

const router = express.Router();
const aumentoController = new AumentoSalarioController();
const authMiddleware = new AuthMiddleware();

// Middleware de autenticação para todas as rotas
router.use((req, res, next) => authMiddleware.validar(req, res, next));

// Rotas de aumento salarial
router.post('/aplicar', (req, res) => aumentoController.aplicarAumento(req, res)
);

router.get('/historico', (req, res) => aumentoController.listarHistorico(req, res)
);

export default router;

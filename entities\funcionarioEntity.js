import toJSON from './toJSON.js';

export default class FuncionarioEntity extends toJSON {
    #fun_id;
    #fun_cpf;
    #fun_nome;
    #fun_salario;
    #fun_datadmissao;
    #fun_datademissao;
    #car_id;

    get fun_id() { return this.#fun_id;} set fun_id(fun_id) { this.#fun_id = fun_id; }
    get fun_cpf() { return this.#fun_cpf;} set fun_cpf(fun_cpf) { this.#fun_cpf = fun_cpf; }
    get fun_nome() { return this.#fun_nome;} set fun_nome(fun_nome) { this.#fun_nome = fun_nome; }
    get fun_salario() { return this.#fun_salario;} set fun_salario(fun_salario) { this.#fun_salario = fun_salario; }
    get fun_datadmissao() { return this.#fun_datadmissao;} set fun_datadmissao(fun_datadmissao) { this.#fun_datadmissao = fun_datadmissao; }
    get fun_datademissao() { return this.#fun_datademissao;} set fun_datademissao(fun_datademissao) { this.#fun_datademissao = fun_datademissao; }
    get car_id() { return this.#car_id;} set car_id(car_id) { this.#car_id = car_id; }

    constructor(cpf, nome, salario, dataadmissao, cargo) {
        super();
        this.fun_cpf = cpf;
        this.fun_nome = nome;
        this.fun_salario = salario;
        this.fun_datadmissao = dataadmissao;
        this.car_id = cargo;
    }
}
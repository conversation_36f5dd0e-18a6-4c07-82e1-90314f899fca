import UsuarioRHRepo from '../repositories/usuariorhRepo.js';
import jwt from 'jsonwebtoken';
var SEGREDO = 'tehe';

export default class AuthMiddleware {

    #repo;

    constructor() {
        this.#repo = new UsuarioRHRepo();
    }

    gerarToken(id, nome, email) {
        try {
            return jwt.sign({
                id: id,
                nome: nome,
                email: email,
            }, SEGREDO, { expiresIn: '5h' } );
        }
        catch(ex) {
            throw ex;
        }
    }

    async validar(req, res, next) {
        let authHeader = req.headers.authorization;
        if(!authHeader || !authHeader.startsWith('Bearer ')) {
            res.status(401).json({msg: "Token não fornecido ou formato inválido!"});
            return;
        }

        let token = authHeader.substring(7); // Remove 'Bearer ' prefix

        try {
            let decoded = jwt.verify(token, SEGREDO);

            // Buscar usuário pelo ID do token
            let usuarios = await this.#repo.BuscarPorId(decoded.id);
            if(usuarios.length == 0) {
                res.status(401).json({msg: "Usuário não encontrado!"});
                return;
            }

            let usuario = usuarios[0];
            if(!usuario.usu_ativo) {
                res.status(401).json({msg: "Usuário inativo!"});
                return;
            }

            req.usuario = usuario;
            next();
        }
        catch(ex) {
            if(ex.name === 'TokenExpiredError') {
                res.status(401).json({msg: "Token expirado!"});
            } else if(ex.name === 'JsonWebTokenError') {
                res.status(401).json({msg: "Token inválido!"});
            } else {
                res.status(401).json({msg: "Erro na validação do token!"});
            }
        }
    }
}
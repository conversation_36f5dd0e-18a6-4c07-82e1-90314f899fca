import FuncionarioRepo from '../repositories/funcionarioRepo.js';
import CargoRepo from '../repositories/cargoRepo.js';
import FuncionarioEntity from '../entities/funcionarioEntity.js';

export default class FuncionarioController {

    #funcionarioRepo;
    #cargoRepo;

    constructor() {
        this.#funcionarioRepo = new FuncionarioRepo();
        this.#cargoRepo = new CargoRepo();
    }

    async cadastrar(req, res) {
        try {
            let { cpf, nome, salario, dataAdmissao, cargoId } = req.body;
            
            if (!cpf || !nome || !salario || !cargoId) {
                res.status(400).json({ msg: "CPF, nome, salário e cargo são obrigatórios!" });
                return;
            }

            if (dataAdmissao) {
                dataAdmissao = new Date(dataAdmissao).toISOString().split('T')[0];
            } else {
                dataAdmissao = new Date().toISOString().split('T')[0];
            }

            let funcionario = new FuncionarioEntity(cpf, nome, salario, dataAdmissao, cargoId);
            await this.#funcionarioRepo.Inserir(funcionario);
            
            res.status(201).json({ msg: "Funcionário cadastrado com sucesso!" });
        } catch (ex) {
            res.status(500).json({ msg: "Erro ao cadastrar funcionário!", error: ex.message });
        }
    }

    async listar(req, res) {
        try {
            let funcionarios = await this.#funcionarioRepo.Listar();
            res.status(200).json(funcionarios);
        } catch (ex) {
            res.status(500).json({ msg: "Erro ao listar funcionários!", error: ex.message });
        }
    }

    async demitir(req, res) {
        try {
            let { id } = req.params;
            
            if (!id) {
                res.status(400).json({ msg: "ID do funcionário é obrigatório!" });
                return;
            }

            let resultado = await this.#funcionarioRepo.Demitir(id);
            
            if (resultado.affectedRows == 0) {
                res.status(404).json({ msg: "Funcionário não encontrado!" });
                return;
            }

            res.status(200).json({ msg: "Funcionário demitido com sucesso!" });
        } catch (ex) {
            res.status(500).json({ msg: "Erro ao demitir funcionário!", error: ex.message });
        }
    }

    async buscarPorId(req, res) {
        try {
            let { id } = req.params;
            
            if (!id) {
                res.status(400).json({ msg: "ID do funcionário é obrigatório!" });
                return;
            }

            let funcionarios = await this.#funcionarioRepo.BuscarPorId(id);
            
            if (!funcionarios) {
                res.status(404).json({ msg: "Funcionário não encontrado!" });
                return;
            }

            res.status(200).json(funcionarios);
        } catch (ex) {
            res.status(500).json({ msg: "Erro ao buscar funcionário!", error: ex.message });
        }
    }

    async listarCargos(req, res) {
        try {
            let cargos = await this.#cargoRepo.Listar();
            res.status(200).json(cargos);
        } catch (ex) {
            res.status(500).json({ msg: "Erro ao listar cargos!", error: ex.message });
        }
    }
}

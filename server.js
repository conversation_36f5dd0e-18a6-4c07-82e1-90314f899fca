import express from 'express'
import swaggerUi from 'swagger-ui-express'
import cookieParser from 'cookie-parser'
import { createRequire } from "module";

// Import routes
import authRoute from './routes/authRoute.js';
import funcionarioRoute from './routes/funcionarioRoute.js';
import aumentoSalarioRoute from './routes/aumentoSalarioRoute.js';
import cargoRoute from './routes/cargoRoute.js';
import folhaRoute from './routes/folhaRoute.js';

const require = createRequire(import.meta.url);
const outputJson = require("./swagger-output.json");

const app = express();

app.use(express.json())
app.use(cookieParser());

// Swagger documentation
app.use("/docs", swaggerUi.serve, swaggerUi.setup(outputJson))

// API Routes
app.use('/auth', authRoute);
app.use('/funcionarios', funcionarioRoute);
app.use('/aumentoSalario', aumentoSalarioRoute);
app.use('/cargos', cargoRoute);
app.use('/folhaPagamento', folhaRoute);

app.listen(5000, function() {
    console.log("Servidor web em funcionamento na porta 5000!");
    console.log("Documentação disponível em: http://localhost:5000/docs");
});
import FolhaPagamentoRepo from '../repositories/folhapagamentoRepo.js';
import ItensFolhaPagamentoRepo from '../repositories/itensfolhapagamentoRepo.js';
import FuncionarioRepo from '../repositories/funcionarioRepo.js';
import FolhaPagamentoEntity from '../entities/folhapagamentoEntity.js';
import ItensFolhaPagamentoEntity from '../entities/itensfolhapagamentoEntity.js';
import Database from '../db/database.js';

export default class FolhaController {

    #folhaRepo;
    #itemRepo;
    #funcionarioRepo;

    constructor() {
        this.#folhaRepo = new FolhaPagamentoRepo();
        this.#itemRepo = new ItensFolhaPagamentoRepo();
        this.#funcionarioRepo = new FuncionarioRepo();
    }

    async gerarFolha(req, res) {
        let db = new Database();
        this.#folhaRepo.db = db;
        this.#itemRepo.db = db;
        this.#funcionarioRepo.db = db;
        await db.AbreTransacao();
        try {
            let {mes, ano} = req.body;
            if (!mes || !ano) {
                res.status(400).send("Mês e ano são obrigatórios!");
                return;
            }
            let folha = new FolhaPagamentoEntity(ano, mes, 0);
            let idFolha = await this.#folhaRepo.Inserir(folha);
            let funcionarios = await this.#funcionarioRepo.Listar();
            let valorTotal = 0;
            for (let funcionario of funcionarios) {
                let item = new ItensFolhaPagamentoEntity(funcionario.fun_salario, funcionario.fun_id, idFolha);
                await this.#itemRepo.Inserir(item);
                valorTotal += parseFloat(funcionario.fun_salario);
            }
            await this.#folhaRepo.AtualizarValorTotal(idFolha, valorTotal);
            await db.Commit();
            res.status(200).json({msg: "Folha gerada com sucesso!"});
        }
        catch(ex) {
            await db.Rollback();
            res.status(500).json({msg: "Erro ao gerar folha!", error: ex});
        }
    }

    async listarFolhas(req, res) {
        try {
            let folhas = await this.#folhaRepo.Listar();
            res.status(200).json(folhas);
        }
        catch(ex) {
            res.status(500).json({msg: "Erro ao listar folhas!"});
        }
    }
    

}
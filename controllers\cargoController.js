import CargoRepo from '../repositories/cargoRepo.js';
import FuncionarioRepo from '../repositories/funcionarioRepo.js';

export default class CargoController {

    #cargoRepo;
    #funcionarioRepo;

    constructor() {
        this.#cargoRepo = new CargoRepo();
        this.#funcionarioRepo = new FuncionarioRepo();
    }

    async listar(req, res) {
        try {
            let cargos = await this.#cargoRepo.Listar();
            res.status(200).json(cargos);
        } catch (ex) {
            res.status(500).json({ msg: "Erro ao listar cargos!", error: ex.message });
        }
    }
}

import Database from '../db/database.js';
import FolhaPagamentoEntity from '../entities/folhapagamentoEntity.js';

export default class FolhaPagamentoRepo {

    #db;

    set db(db) { this.#db = db; }

    constructor() {
        this.#db = new Database();
    }

    async Inserir(folha) {
        try {
            let sql = "INSERT INTO tb_folhapagamento (fol_ano, fol_mes, fol_valortotal) VALUES (?, ?, ?)";
            let valores = [folha.fol_ano, folha.fol_mes, folha.fol_valortotal];
            let id = await this.#db.ExecutaComandoLastInserted(sql, valores);
            return id;
        } catch (err) {
            throw err;
        }
    }

    async Listar() {
        let sql = "SELECT * FROM tb_folhapagamento";
        let results = await this.#db.ExecutaComando(sql, []);
        return results;
    }

    async ListarPorMesAno(mes, ano) {
        let sql = "SELECT * FROM tb_folhapagamento WHERE fol_mes = ? AND fol_ano = ?";
        let valores = [mes, ano];
        let results = await this.#db.ExecutaComando(sql, valores);
        return results;
    }

    async AtualizarValorTotal(id, valor) {
        let sql = "UPDATE tb_folhapagamento SET fol_valortotal = ? WHERE fol_id = ?";
        let valores = [valor, id];
        let results = await this.#db.ExecutaComandoNonQuery(sql, valores);
        return results;
    }

}
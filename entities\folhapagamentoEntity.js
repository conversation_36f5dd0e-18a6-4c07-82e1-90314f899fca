import toJSON from './toJSON.js';

export default class FolhaPagamentoEntity extends toJSON {
    #fol_id;
    #fol_ano;
    #fol_mes;
    #fol_valortotal;

    get fol_id() { return this.#fol_id;} set fol_id(fol_id) { this.#fol_id = fol_id; }
    get fol_ano() { return this.#fol_ano;} set fol_ano(fol_ano) { this.#fol_ano = fol_ano; }
    get fol_mes() { return this.#fol_mes;} set fol_mes(fol_mes) { this.#fol_mes = fol_mes; }
    get fol_valortotal() { return this.#fol_valortotal;} set fol_valortotal(fol_valortotal) { this.#fol_valortotal = fol_valortotal; }

    constructor(ano, mes, valor) {
        super();
        this.fol_ano = ano;
        this.fol_mes = mes;
        this.fol_valortotal = valor;
    }
}